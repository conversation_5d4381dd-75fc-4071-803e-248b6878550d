# mypy: disable - error - code = "no-untyped-def,misc"
import pathlib
import os
from fastapi import FastAP<PERSON>, Request, Response
from fastapi.staticfiles import StaticFiles
import fastapi.exceptions
from pydantic import BaseModel

from agent.configuration import Configuration
from agent.providers import get_provider, get_model_name

# Define the FastAPI app
app = FastAPI()


class ModelConfigResponse(BaseModel):
    """Response model for the configuration API endpoint."""
    model_provider: str
    query_generator_model: str
    reflection_model: str
    answer_model: str
    provider_configured: bool


@app.get("/api/config", response_model=ModelConfigResponse)
async def get_model_config():
    """Get the current model configuration."""
    try:
        # Get configuration from environment
        config = Configuration.from_runnable_config()

        # Get the provider
        provider = get_provider(config.model_provider)

        # Get actual model names being used
        query_model = get_model_name(provider, "query_generator",
                                   os.getenv("QUERY_GENERATOR_MODEL"))
        reflection_model = get_model_name(provider, "reflection",
                                        os.getenv("REFLECTION_MODEL"))
        answer_model = get_model_name(provider, "answer",
                                    os.getenv("ANSWER_MODEL"))

        return ModelConfigResponse(
            model_provider=config.model_provider,
            query_generator_model=query_model,
            reflection_model=reflection_model,
            answer_model=answer_model,
            provider_configured=provider.validate_configuration()
        )
    except Exception as e:
        # Return default configuration if there's an error
        return ModelConfigResponse(
            model_provider="unknown",
            query_generator_model="unknown",
            reflection_model="unknown",
            answer_model="unknown",
            provider_configured=False
        )


def create_frontend_router(build_dir="../frontend/dist"):
    """Creates a router to serve the React frontend.

    Args:
        build_dir: Path to the React build directory relative to this file.

    Returns:
        A Starlette application serving the frontend.
    """
    build_path = pathlib.Path(__file__).parent.parent.parent / build_dir
    static_files_path = build_path / "assets"  # Vite uses 'assets' subdir

    if not build_path.is_dir() or not (build_path / "index.html").is_file():
        print(
            f"WARN: Frontend build directory not found or incomplete at {build_path}. Serving frontend will likely fail."
        )
        # Return a dummy router if build isn't ready
        from starlette.routing import Route

        async def dummy_frontend(request):
            return Response(
                "Frontend not built. Run 'npm run build' in the frontend directory.",
                media_type="text/plain",
                status_code=503,
            )

        return Route("/{path:path}", endpoint=dummy_frontend)

    build_dir = pathlib.Path(build_dir)

    react = FastAPI(openapi_url="")
    react.mount(
        "/assets", StaticFiles(directory=static_files_path), name="static_assets"
    )

    @react.get("/{path:path}")
    async def handle_catch_all(request: Request, path: str):
        fp = build_path / path
        if not fp.exists() or not fp.is_file():
            fp = build_path / "index.html"
        return fastapi.responses.FileResponse(fp)

    return react


# Mount the frontend under /app to not conflict with the LangGraph API routes
app.mount(
    "/app",
    create_frontend_router(),
    name="frontend",
)
