import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Brain, Cpu, MessageSquare, Zap } from "lucide-react";
import { useModelConfig } from "@/hooks/useModelConfig";

interface ModelConfigDisplayProps {
  className?: string;
}

export const ModelConfigDisplay: React.FC<ModelConfigDisplayProps> = ({
  className = "",
}) => {
  const { config, loading, error } = useModelConfig();

  if (loading) {
    return (
      <div className={`text-xs text-neutral-500 ${className}`}>
        Loading model configuration...
      </div>
    );
  }

  if (error || !config) {
    return (
      <div className={`text-xs text-neutral-500 ${className}`}>
        Powered by <PERSON><PERSON><PERSON><PERSON>
      </div>
    );
  }

  const getProviderDisplayName = (provider: string) => {
    switch (provider.toLowerCase()) {
      case "gemini":
        return "Google Gemini";
      case "openrouter":
        return "OpenRouter.ai";
      default:
        return provider;
    }
  };

  const getModelDisplayName = (modelName: string) => {
    // Truncate long model names for display
    if (modelName.length > 25) {
      return modelName.substring(0, 22) + "...";
    }
    return modelName;
  };

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case "gemini":
        return <Zap className="h-3 w-3" />;
      case "openrouter":
        return <Cpu className="h-3 w-3" />;
      default:
        return <Brain className="h-3 w-3" />;
    }
  };

  return (
    <div className={`text-xs text-neutral-500 space-y-2 ${className}`}>
      <div className="flex items-center gap-2">
        {getProviderIcon(config.model_provider)}
        <span>
          Powered by {getProviderDisplayName(config.model_provider)} and LangChain LangGraph
        </span>
        {config.provider_configured && (
          <Badge variant="secondary" className="text-xs px-1 py-0">
            ✓
          </Badge>
        )}
      </div>
      
      <Card className="p-2 bg-neutral-800 border-neutral-700">
        <div className="text-xs text-neutral-400 mb-1">Current Model Configuration:</div>
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Brain className="h-3 w-3 text-blue-400" />
            <span className="text-neutral-300">Query:</span>
            <span className="text-neutral-400 font-mono text-xs">
              {getModelDisplayName(config.query_generator_model)}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <MessageSquare className="h-3 w-3 text-purple-400" />
            <span className="text-neutral-300">Reflection:</span>
            <span className="text-neutral-400 font-mono text-xs">
              {getModelDisplayName(config.reflection_model)}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Zap className="h-3 w-3 text-green-400" />
            <span className="text-neutral-300">Answer:</span>
            <span className="text-neutral-400 font-mono text-xs">
              {getModelDisplayName(config.answer_model)}
            </span>
          </div>
        </div>
      </Card>
    </div>
  );
};
