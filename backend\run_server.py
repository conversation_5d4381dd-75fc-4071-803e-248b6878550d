#!/usr/bin/env python3
"""
Simple server runner for testing the API endpoints.
"""

import sys
import os
import uvicorn

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

if __name__ == "__main__":
    # Import the app after setting up the path
    from agent.app import app
    
    print("🚀 Starting development server...")
    print("📡 API endpoint available at: http://localhost:2024/api/config")
    print("🌐 Frontend will be available at: http://localhost:2024/app")
    print("Press Ctrl+C to stop the server")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=2024,
        reload=True,
        reload_dirs=["src"]
    )
