# Production Deployment Guide

This guide explains how to deploy the LangGraph agent in production without LangSmith Cloud dependencies.

## Overview

The LangGraph agent has been configured to run standalone in production environments without requiring LangSmith Cloud integration. This simplifies deployment and reduces external dependencies while maintaining full functionality.

## Key Changes

### ✅ Removed Dependencies
- **LangSmith Cloud**: No longer required for production deployment
- **LANGSMITH_API_KEY**: Removed from mandatory environment variables
- **External monitoring**: Agent runs independently

### ✅ Maintained Functionality
- **Full LangGraph capabilities**: All agent features work without LangSmith
- **OpenRouter.ai support**: Multiple model provider options
- **Streaming responses**: Real-time agent execution
- **State persistence**: Redis and PostgreSQL integration
- **Error handling**: Robust retry mechanisms

## Production Deployment Options

### Option 1: Docker Compose (Recommended)

**1. Build the Docker image:**
```bash
docker build -t gemini-fullstack-langgraph -f Dockerfile .
```

**2. Deploy with Google Gemini:**
```bash
GEMINI_API_KEY=your_api_key docker-compose up -d
```

**3. Deploy with OpenRouter.ai:**
```bash
MODEL_PROVIDER=openrouter OPENROUTER_API_KEY=your_api_key docker-compose up -d
```

### Option 2: Standalone Container

**1. Build and run the container:**
```bash
docker build -t langgraph-agent .
docker run -d \
  -p 8123:8000 \
  -e MODEL_PROVIDER=gemini \
  -e GEMINI_API_KEY=your_api_key \
  -e REDIS_URI=redis://your-redis:6379 \
  -e POSTGRES_URI=***********************************/db \
  langgraph-agent
```

## Environment Configuration

### Required Variables

Choose one model provider:

**For Google Gemini:**
```bash
MODEL_PROVIDER=gemini
GEMINI_API_KEY=your_gemini_api_key
```

**For OpenRouter.ai:**
```bash
MODEL_PROVIDER=openrouter
OPENROUTER_API_KEY=your_openrouter_api_key
```

### Infrastructure Variables

```bash
REDIS_URI=redis://localhost:6379
POSTGRES_URI=postgresql://user:password@localhost:5432/dbname
```

### Optional Configuration

```bash
# Custom model selection
QUERY_GENERATOR_MODEL=gemini-2.0-flash
REFLECTION_MODEL=gemini-2.5-flash-preview-04-17
ANSWER_MODEL=gemini-2.5-pro-preview-05-06

# Debug logging
DEBUG=false
```

## Health Checks

The application includes built-in health checks:

- **Backend API**: `http://localhost:8123/health`
- **Redis**: Automatic connection validation
- **PostgreSQL**: Database connectivity checks

## Monitoring and Logging

### Built-in Monitoring

The agent includes comprehensive logging without external dependencies:

- **Request/Response logging**: All API interactions
- **Error tracking**: Detailed error messages and stack traces
- **Performance metrics**: Response times and resource usage
- **Agent state tracking**: Step-by-step execution logs

### Log Levels

```bash
# Set log level (optional)
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR
```

### Custom Monitoring Integration

If you want to add monitoring later, you can integrate:

- **Prometheus**: Add metrics endpoints
- **Grafana**: Visualization dashboards
- **ELK Stack**: Centralized logging
- **LangSmith**: Optional tracing (if needed)

## Security Considerations

### API Key Management

- Store API keys in environment variables
- Use secrets management in production (AWS Secrets Manager, Azure Key Vault, etc.)
- Rotate keys regularly
- Limit API key permissions to minimum required

### Network Security

```yaml
# docker-compose.yml security example
services:
  langgraph-api:
    networks:
      - internal
    ports:
      - "127.0.0.1:8123:8000"  # Bind to localhost only

networks:
  internal:
    driver: bridge
```

## Scaling and Performance

### Horizontal Scaling

Deploy multiple instances behind a load balancer:

```bash
# Scale with Docker Compose
docker-compose up --scale langgraph-api=3
```

### Resource Requirements

**Minimum:**
- CPU: 1 core
- Memory: 2GB RAM
- Storage: 10GB

**Recommended:**
- CPU: 2+ cores
- Memory: 4GB+ RAM
- Storage: 50GB+ SSD

## Troubleshooting

### Common Issues

**1. Model Provider Connection Errors**
```bash
# Check API key validity
curl -H "Authorization: Bearer $GEMINI_API_KEY" https://generativelanguage.googleapis.com/v1/models
```

**2. Redis Connection Issues**
```bash
# Test Redis connectivity
redis-cli -u $REDIS_URI ping
```

**3. PostgreSQL Connection Issues**
```bash
# Test database connectivity
psql $POSTGRES_URI -c "SELECT 1;"
```

### Debug Mode

Enable detailed logging:
```bash
DEBUG=true docker-compose up
```

## Migration from LangSmith

If you were previously using LangSmith:

1. **Remove environment variables:**
   - `LANGSMITH_API_KEY`
   - `LANGCHAIN_PROJECT`
   - `LANGSMITH_TRACING`

2. **Update deployment scripts:**
   - Remove LangSmith references from docker-compose.yml
   - Update environment variable lists

3. **Verify functionality:**
   - Test all agent workflows
   - Confirm streaming responses work
   - Validate error handling

## Support

For deployment issues:

1. Check the [troubleshooting section](#troubleshooting)
2. Review application logs
3. Verify environment configuration
4. Test individual components (Redis, PostgreSQL, model providers)

The agent is designed to be self-contained and should work reliably without external monitoring services.
