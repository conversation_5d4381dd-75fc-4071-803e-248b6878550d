# Architecture Documentation

## Project Overview

The **Gemini Fullstack LangGraph Quickstart** is a sophisticated research-augmented conversational AI application that demonstrates the integration of a React frontend with a LangGraph-powered backend agent. The system is designed to perform comprehensive research on user queries by dynamically generating search terms, querying the web using Google Search API, reflecting on results to identify knowledge gaps, and iteratively refining searches until it can provide well-supported answers with citations.

### Primary Purpose

- **Intelligent Research Agent**: Automatically conducts multi-step web research with iterative refinement
- **Citation-Based Responses**: Provides answers backed by verifiable sources and citations
- **Real-time Streaming**: Offers live updates of research progress through WebSocket connections
- **Fullstack Demonstration**: Showcases modern AI agent architecture patterns using LangGraph

## Architecture Diagram

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React UI Components]
        WS[WebSocket Client]
        STATE[React State Management]
    end
    
    subgraph "Backend Layer"
        API[FastAPI Server]
        LG[LangGraph Agent]
        REDIS[Redis Pub/Sub]
        PG[PostgreSQL Database]
    end
    
    subgraph "External Services"
        GEMINI[Google Gemini API]
        SEARCH[Google Search API]
    end
    
    subgraph "LangGraph Agent Flow"
        START_NODE[Start]
        GEN[Generate Query Node]
        WEB[Web Research Node]
        REF[Reflection Node]
        FIN[Finalize Answer Node]
        END_NODE[End]
        
        START_NODE --> GEN
        GEN --> WEB
        WEB --> REF
        REF --> WEB
        REF --> FIN
        FIN --> END_NODE
    end
    
    UI --> WS
    WS --> API
    API --> LG
    LG --> REDIS
    LG --> PG
    LG --> GEMINI
    LG --> SEARCH
    
    style LG fill:#e1f5fe
    style GEMINI fill:#fff3e0
    style SEARCH fill:#fff3e0
```

## Technology Stack

### Frontend Technologies

- **React 19.0.0**: Modern UI library with latest features
- **TypeScript 5.7.2**: Type-safe JavaScript development
- **Vite 6.3.4**: Fast build tool and development server
- **Tailwind CSS 4.1.5**: Utility-first CSS framework
- **Shadcn UI**: High-quality component library built on Radix UI
- **React Router DOM 7.5.3**: Client-side routing
- **React Markdown 9.0.3**: Markdown rendering for AI responses
- **Lucide React 0.508.0**: Icon library

### Backend Technologies

- **Python 3.11+**: Modern Python runtime
- **LangGraph 0.2.6+**: State-based agent orchestration framework
- **LangChain 0.3.19+**: LLM application framework
- **FastAPI**: High-performance web framework
- **Google Gemini API**: Large language models for reasoning
- **Google Search API**: Web search capabilities
- **Redis 6**: Pub/sub messaging and caching
- **PostgreSQL 16**: Persistent data storage

### Development & Deployment

- **Docker & Docker Compose**: Containerization and orchestration
- **UV**: Fast Python package installer
- **ESLint & Ruff**: Code linting and formatting
- **Makefile**: Build automation

## Directory Structure

```plaintext
gemini-fullstack-langgraph-quickstart/
├── frontend/                          # React frontend application
│   ├── src/
│   │   ├── components/                # React components
│   │   │   ├── ui/                   # Shadcn UI components
│   │   │   ├── ActivityTimeline.tsx  # Research progress visualization
│   │   │   ├── ChatMessagesView.tsx  # Chat interface
│   │   │   ├── InputForm.tsx         # User input form
│   │   │   └── WelcomeScreen.tsx     # Landing page
│   │   ├── lib/
│   │   │   └── utils.ts              # Utility functions
│   │   ├── App.tsx                   # Main application component
│   │   ├── main.tsx                  # Application entry point
│   │   └── global.css                # Global styles
│   ├── public/                       # Static assets
│   ├── package.json                  # Frontend dependencies
│   ├── vite.config.ts               # Vite configuration
│   └── tsconfig.json                # TypeScript configuration
├── backend/                          # LangGraph backend agent
│   ├── src/agent/
│   │   ├── __init__.py
│   │   ├── app.py                   # FastAPI application
│   │   ├── graph.py                 # LangGraph agent definition
│   │   ├── state.py                 # State management schemas
│   │   ├── configuration.py         # Agent configuration
│   │   ├── prompts.py               # LLM prompt templates
│   │   ├── tools_and_schemas.py     # Pydantic schemas
│   │   └── utils.py                 # Helper functions
│   ├── pyproject.toml               # Python dependencies
│   ├── langgraph.json              # LangGraph configuration
│   └── Makefile                     # Backend build commands
├── docker-compose.yml               # Production deployment
├── Dockerfile                       # Multi-stage container build
├── Makefile                         # Development commands
└── README.md                        # Project documentation
```

### Key Directory Purposes

- **`frontend/src/components/`**: Modular React components with clear separation of concerns
- **`backend/src/agent/`**: Core agent logic with clean architecture patterns
- **`frontend/src/components/ui/`**: Reusable UI components following design system principles
- **Configuration files**: Environment-specific settings and build configurations

## Core Components

### Backend Agent Components

#### 1. LangGraph Agent (`graph.py`)

The heart of the system is a stateful LangGraph agent that orchestrates the research process through interconnected nodes:

**Key Nodes:**

- **`generate_query`**: Uses Gemini 2.0 Flash to create optimized search queries from user input
- **`web_research`**: Executes parallel web searches using Google Search API with Gemini models
- **`reflection`**: Analyzes research results to identify knowledge gaps using structured output
- **`finalize_answer`**: Synthesizes findings into coherent responses with proper citations

**Agent Flow:**

```python
# Simplified agent structure
builder = StateGraph(OverallState, config_schema=Configuration)
builder.add_node("generate_query", generate_query)
builder.add_node("web_research", web_research)
builder.add_node("reflection", reflection)
builder.add_node("finalize_answer", finalize_answer)

# Define flow
builder.add_edge(START, "generate_query")
builder.add_conditional_edges("generate_query", continue_to_web_research, ["web_research"])
builder.add_edge("web_research", "reflection")
builder.add_conditional_edges("reflection", evaluate_research, ["web_research", "finalize_answer"])
```

#### 2. State Management (`state.py`)

Implements typed state schemas using TypedDict for robust data flow:

**Core State Types:**

- **`OverallState`**: Main graph state with message history, search results, and configuration
- **`ReflectionState`**: Handles knowledge gap analysis and follow-up query generation
- **`QueryGenerationState`**: Manages search query lists and rationales
- **`WebSearchState`**: Contains individual search query execution context

#### 3. Configuration System (`configuration.py`)

Pydantic-based configuration management with environment variable support:

**Configurable Parameters:**

- Model selection (query generation, reflection, answer synthesis)
- Research loop limits and initial query counts
- Temperature and retry settings for different LLM tasks

#### 4. Prompt Engineering (`prompts.py`)

Sophisticated prompt templates optimized for different agent tasks:

**Prompt Categories:**

- **Query Generation**: Creates diverse, targeted search queries
- **Web Research**: Guides comprehensive information gathering
- **Reflection**: Identifies knowledge gaps and generates follow-ups
- **Answer Synthesis**: Produces well-cited final responses

### Frontend Components

#### 1. Main Application (`App.tsx`)

Central component managing WebSocket connections and state:

**Key Features:**

- **Real-time Streaming**: Uses `@langchain/langgraph-sdk/react` for live agent updates
- **Event Processing**: Transforms raw agent events into UI-friendly timeline data
- **Configuration Management**: Handles research effort levels and model selection
- **State Coordination**: Manages message history and activity timelines

#### 2. Chat Interface (`ChatMessagesView.tsx`)

Sophisticated chat UI with markdown rendering and citation support:

**Components:**

- **Message Bubbles**: Separate rendering for human and AI messages
- **Copy Functionality**: One-click copying of responses
- **Activity Integration**: Embedded research timeline for each response
- **Markdown Support**: Rich text rendering with proper styling

#### 3. Activity Timeline (`ActivityTimeline.tsx`)

Real-time visualization of agent research progress:

**Features:**

- **Live Updates**: Shows current research steps as they happen
- **Collapsible Interface**: Expandable timeline with detailed progress
- **Icon Mapping**: Visual indicators for different research phases
- **Loading States**: Animated indicators for ongoing operations

#### 4. Input System (`InputForm.tsx` & `WelcomeScreen.tsx`)

User interaction components with advanced configuration:

**Capabilities:**

- **Effort Level Selection**: Low/Medium/High research intensity
- **Model Selection**: Choice of Gemini models for different tasks
- **Validation**: Input sanitization and error handling
- **Responsive Design**: Mobile-friendly interface

## Data Flow

### Request-Response Cycle

1. **User Input Processing**
   - User submits query through React frontend
   - Frontend validates input and configures research parameters
   - WebSocket connection established with backend

2. **Agent Initialization**
   - LangGraph agent receives user message and configuration
   - Initial state created with research parameters
   - Agent begins execution at `generate_query` node

3. **Query Generation Phase**

   ```python
   # Example query generation
   def generate_query(state: OverallState, config: RunnableConfig):
       llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash")
       structured_llm = llm.with_structured_output(SearchQueryList)
       result = structured_llm.invoke(formatted_prompt)
       return {"query_list": result.query}
   ```

4. **Parallel Web Research**
   - Multiple search queries executed simultaneously
   - Google Search API integration with Gemini models
   - Results processed and citations extracted

5. **Reflection and Iteration**
   - Agent analyzes gathered information for completeness
   - Identifies knowledge gaps using structured reasoning
   - Generates follow-up queries if needed (up to max loops)

6. **Response Synthesis**
   - Final answer generated with proper citations
   - Sources deduplicated and formatted
   - Response streamed back to frontend in real-time

### State Management Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as Agent
    participant G as Gemini API
    participant S as Search API

    U->>F: Submit Query
    F->>A: WebSocket Message
    A->>G: Generate Search Queries
    G->>A: Query List

    loop For Each Query
        A->>S: Execute Search
        S->>A: Search Results
        A->>G: Process Results
        G->>A: Processed Summary
    end

    A->>G: Reflect on Results
    G->>A: Knowledge Gap Analysis

    alt Knowledge Gap Exists
        A->>G: Generate Follow-up Queries
        G->>A: New Query List
        Note over A: Repeat Research Loop
    else Sufficient Information
        A->>G: Synthesize Final Answer
        G->>A: Final Response
        A->>F: Stream Response
        F->>U: Display Answer
    end
```

## Key Design Patterns

### 1. State-Based Agent Architecture

The system implements a **stateful agent pattern** using LangGraph's StateGraph:

- **Immutable State Updates**: Each node returns state updates that are merged
- **Type Safety**: TypedDict schemas ensure data consistency
- **Conditional Routing**: Dynamic flow control based on agent reasoning

### 2. Parallel Processing Pattern

Web research operations are executed in parallel for efficiency:

```python
def continue_to_web_research(state: QueryGenerationState):
    return [
        Send("web_research", {"search_query": query, "id": idx})
        for idx, query in enumerate(state["query_list"])
    ]
```

### 3. Structured Output Pattern

Consistent use of Pydantic schemas for LLM outputs:

```python
class Reflection(BaseModel):
    is_sufficient: bool
    knowledge_gap: str
    follow_up_queries: List[str]
```

### 4. Event-Driven Frontend

React components respond to real-time agent events:

- **WebSocket Integration**: Live updates from backend
- **Event Processing**: Transform agent events into UI state
- **Optimistic Updates**: Immediate UI feedback

### 5. Separation of Concerns

Clear architectural boundaries:

- **Presentation Layer**: React components handle UI logic
- **Business Logic**: LangGraph agent manages research workflow
- **Data Layer**: Redis/PostgreSQL handle persistence
- **External Services**: Isolated API integrations

## Configuration Management

### Environment Variables

**Required Configuration:**

```bash
# Model Provider Selection
MODEL_PROVIDER="gemini"  # or "openrouter"

# API Keys (choose one based on provider)
GEMINI_API_KEY="your_gemini_api_key"
OPENROUTER_API_KEY="your_openrouter_api_key"

# Production Database URLs
REDIS_URI="redis://localhost:6379"
POSTGRES_URI="postgres://user:pass@localhost:5432/db"
```

### Agent Configuration (`configuration.py`)

**Model Selection:**

- `query_generator_model`: "gemini-2.0-flash" (default)
- `reflection_model`: "gemini-2.5-flash-preview-04-17"
- `answer_model`: "gemini-2.5-pro-preview-05-06"

**Research Parameters:**

- `number_of_initial_queries`: 3 (default)
- `max_research_loops`: 2 (default)

### Frontend Configuration

**Development vs Production:**

```typescript
const apiUrl = import.meta.env.DEV
  ? "http://localhost:2024"      // Development
  : "http://localhost:8123";     // Production
```

**Research Effort Mapping:**

```typescript
// User-friendly effort levels map to agent parameters
switch (effort) {
  case "low":    return { queries: 1, loops: 1 };
  case "medium": return { queries: 3, loops: 3 };
  case "high":   return { queries: 5, loops: 10 };
}
```

## Database Schema

### PostgreSQL Tables (LangGraph Managed)

The system uses LangGraph's built-in persistence layer with the following key tables:

**Core Tables:**

- **`assistants`**: Agent configuration and metadata
- **`threads`**: Conversation sessions and context
- **`runs`**: Individual agent execution instances
- **`checkpoints`**: State snapshots for resumability
- **`checkpoint_blobs`**: Large state data storage

**State Management:**

```sql
-- Simplified checkpoint structure
CREATE TABLE checkpoints (
    thread_id UUID,
    checkpoint_id UUID,
    parent_checkpoint_id UUID,
    checkpoint JSONB,
    metadata JSONB,
    created_at TIMESTAMP
);
```

### Redis Data Structures

**Pub/Sub Channels:**

- **Agent Events**: Real-time streaming of agent progress
- **State Updates**: Live state synchronization
- **Error Notifications**: System health monitoring

**Caching Strategy:**

- **Search Results**: Temporary caching of web research data
- **Session Data**: User interaction state
- **Rate Limiting**: API call throttling

## API Documentation

### WebSocket Endpoints

**Primary Connection:**

```plaintext
ws://localhost:2024/ws  (Development)
ws://localhost:8123/ws  (Production)
```

**Message Format:**

```typescript
interface AgentMessage {
  messages: Message[];
  initial_search_query_count: number;
  max_research_loops: number;
  reasoning_model: string;
}
```

### REST API Endpoints

**Health Check:**

```plaintext
GET /health
Response: { "status": "healthy" }
```

**Agent Configuration:**

```plaintext
GET /api/config
Response: {
  "available_models": string[],
  "default_config": Configuration
}
```

### Frontend API Integration

**LangGraph SDK Usage:**

```typescript
const thread = useStream<AgentState>({
  apiUrl: "http://localhost:2024",
  assistantId: "agent",
  messagesKey: "messages",
  onFinish: (event) => console.log(event)
});
```

**Event Handling:**

```typescript
// Process real-time agent events
useEffect(() => {
  if (thread.events) {
    const processedEvents = thread.events.map(transformEvent);
    setProcessedEventsTimeline(processedEvents);
  }
}, [thread.events]);
```

## Deployment Architecture

### Development Environment

**Local Development Stack:**

```bash
# Backend (Port 2024)
cd backend && langgraph dev

# Frontend (Port 5173)
cd frontend && npm run dev

# Combined
make dev
```

**Development Features:**

- Hot reloading for both frontend and backend
- LangGraph Studio UI for agent debugging
- Real-time log streaming
- Environment variable loading from `.env`

### Production Deployment

**Docker Multi-Stage Build:**

```dockerfile
# Stage 1: Build React Frontend
FROM node:20-alpine AS frontend-builder
WORKDIR /app/frontend
COPY frontend/ ./
RUN npm install && npm run build

# Stage 2: Python Backend with Frontend
FROM langchain/langgraph-api:3.11
COPY --from=frontend-builder /app/frontend/dist /deps/frontend/dist
COPY backend/ /deps/backend
RUN uv pip install --system -e /deps/backend
```

**Production Stack (docker-compose.yml):**

- **LangGraph API**: Main application server
- **PostgreSQL 16**: Persistent data storage
- **Redis 6**: Pub/sub and caching
- **Health Checks**: Automated service monitoring

**Scaling Considerations:**

- **Horizontal Scaling**: Multiple LangGraph API instances
- **Database Sharding**: Thread-based partitioning
- **Redis Clustering**: High-availability pub/sub
- **Load Balancing**: Nginx or cloud load balancers

## Dependencies and Package Management

### Backend Dependencies (pyproject.toml)

**Core Framework Dependencies:**

```toml
dependencies = [
    "langgraph>=0.2.6",           # Agent orchestration framework
    "langchain>=0.3.19",          # LLM application framework
    "langchain-google-genai",     # Google Gemini integration
    "python-dotenv>=1.0.1",      # Environment variable management
    "langgraph-sdk>=0.1.57",     # LangGraph client SDK
    "langgraph-cli",              # Development tools
    "langgraph-api",              # Production API server
    "fastapi",                    # Web framework
    "google-genai",               # Google AI client
]
```

**Development Dependencies:**

```toml
[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[dependency-groups]
dev = [
    "langgraph-cli[inmem]>=0.1.71",
    "pytest>=8.3.5",
]
```

**Key Dependency Purposes:**

- **LangGraph**: Provides stateful agent orchestration and graph-based workflows
- **LangChain**: Offers LLM abstractions and integrations
- **FastAPI**: High-performance web framework for API endpoints
- **Google Gemini**: Advanced language models for reasoning and search
- **Pydantic**: Data validation and settings management (via LangChain)

### Frontend Dependencies (package.json)

**Core React Dependencies:**

```json
{
  "dependencies": {
    "react": "^19.0.0",                    // Latest React with concurrent features
    "react-dom": "^19.0.0",               // DOM rendering
    "@langchain/langgraph-sdk": "^0.0.74", // LangGraph client integration
    "react-router-dom": "^7.5.3",         // Client-side routing
    "react-markdown": "^9.0.3",           // Markdown rendering
  }
}
```

**UI Framework Dependencies:**

```json
{
  "dependencies": {
    "@radix-ui/react-scroll-area": "^1.2.8",  // Accessible scroll components
    "@radix-ui/react-select": "^2.2.4",       // Select dropdowns
    "@radix-ui/react-slot": "^1.2.2",         // Composition utilities
    "@radix-ui/react-tabs": "^1.1.11",        // Tab components
    "@radix-ui/react-tooltip": "^1.2.6",      // Tooltip components
    "tailwindcss": "^4.1.5",                  // Utility-first CSS
    "@tailwindcss/vite": "^4.1.5",           // Vite integration
    "lucide-react": "^0.508.0",              // Icon library
  }
}
```

**Development Tools:**

```json
{
  "devDependencies": {
    "@vitejs/plugin-react-swc": "^3.9.0",     // Fast React refresh
    "typescript": "~5.7.2",                   // Type checking
    "eslint": "^9.22.0",                      // Code linting
    "vite": "^6.3.4",                         // Build tool
  }
}
```

## Security Considerations

### Authentication and Authorization

**API Key Management:**

- **Environment Variables**: Secure storage of sensitive credentials
- **Key Rotation**: Support for updating API keys without downtime
- **Scope Limitation**: Minimal required permissions for external services

**Access Control:**

```python
# Environment variable validation
if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")
```

### Data Security

**Input Validation:**

- **Frontend Sanitization**: XSS prevention in user inputs
- **Backend Validation**: Pydantic schemas for type safety
- **Rate Limiting**: Protection against abuse

**Data Privacy:**

- **No Persistent User Data**: Conversations not stored long-term
- **Citation Anonymization**: URL shortening for privacy
- **Secure Transmission**: HTTPS/WSS in production

### Infrastructure Security

**Container Security:**

```dockerfile
# Use official base images
FROM docker.io/langchain/langgraph-api:3.11

# Non-root user execution
RUN adduser --disabled-password --gecos '' appuser
USER appuser
```

**Network Security:**

- **Internal Communication**: Services communicate via Docker networks
- **Port Exposure**: Minimal external port exposure
- **Health Checks**: Automated service monitoring

## Performance Considerations

### Caching Strategies

**Multi-Level Caching:**

1. **Browser Cache**: Static assets and API responses
2. **Redis Cache**: Search results and session data
3. **CDN Cache**: Global asset distribution (production)

**Cache Implementation:**

```python
# Redis caching for search results
@cache(expire=3600)  # 1 hour cache
def cached_search_results(query: str) -> Dict:
    return execute_search(query)
```

### Optimization Techniques

**Frontend Optimizations:**

- **Code Splitting**: Lazy loading of components
- **Bundle Optimization**: Tree shaking and minification
- **Image Optimization**: WebP format and responsive images
- **Virtual Scrolling**: Efficient rendering of large lists

**Backend Optimizations:**

- **Parallel Processing**: Concurrent web searches
- **Connection Pooling**: Database connection reuse
- **Async Operations**: Non-blocking I/O operations
- **Structured Output**: Efficient LLM response parsing

### Scalability Features

**Horizontal Scaling:**

```yaml
# docker-compose scaling
services:
  langgraph-api:
    deploy:
      replicas: 3
    depends_on:
      - langgraph-redis
      - langgraph-postgres
```

**Performance Monitoring:**

- **LangSmith Integration**: Agent performance tracking
- **Metrics Collection**: Response times and error rates
- **Resource Monitoring**: CPU, memory, and network usage
- **Auto-scaling**: Container orchestration support

**Load Testing Considerations:**

- **Concurrent Users**: WebSocket connection limits
- **API Rate Limits**: External service quotas
- **Database Performance**: Query optimization and indexing
- **Memory Management**: State cleanup and garbage collection
