#!/usr/bin/env python3
"""
Test script for the new API endpoint.
"""

import sys
import os
import asyncio
from fastapi.testclient import TestClient

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.app import app

def test_config_endpoint():
    """Test the /api/config endpoint."""
    print("🧪 Testing /api/config endpoint")
    print("=" * 50)
    
    # Create a test client
    client = TestClient(app)
    
    try:
        # Make a request to the config endpoint
        response = client.get("/api/config")
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API endpoint working correctly!")
            print(f"   - Model Provider: {data.get('model_provider')}")
            print(f"   - Query Generator: {data.get('query_generator_model')}")
            print(f"   - Reflection: {data.get('reflection_model')}")
            print(f"   - Answer: {data.get('answer_model')}")
            print(f"   - Provider Configured: {data.get('provider_configured')}")
            return True
        else:
            print(f"❌ API endpoint failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API endpoint: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_config_endpoint()
