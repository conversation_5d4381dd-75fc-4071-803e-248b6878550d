import { useState, useEffect } from "react";

export interface ModelConfig {
  model_provider: string;
  query_generator_model: string;
  reflection_model: string;
  answer_model: string;
  provider_configured: boolean;
}

export const useModelConfig = () => {
  const [config, setConfig] = useState<ModelConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const apiUrl = import.meta.env.DEV
          ? "http://localhost:2024"
          : "http://localhost:8123";
        
        const response = await fetch(`${apiUrl}/api/config`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setConfig(data);
      } catch (err) {
        console.error("Failed to fetch model configuration:", err);
        setError("Failed to load model configuration");
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  return { config, loading, error };
};
