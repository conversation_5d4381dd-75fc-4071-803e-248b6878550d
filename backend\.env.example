# Model Provider Selection (gemini or openrouter)
# MODEL_PROVIDER=gemini

# Google Gemini API Configuration
# GEMINI_API_KEY=your_gemini_api_key_here

# OpenRouter API Configuration (Alternative to Gemini)
# OPENROUTER_API_KEY=sk-or-your_openrouter_api_key_here

# Custom Model Configuration (Optional)
# Override default models for specific tasks
# QUERY_GENERATOR_MODEL=gemini-2.0-flash
# REFLECTION_MODEL=gemini-2.5-flash-preview-04-17
# ANSWER_MODEL=gemini-2.5-pro-preview-05-06

# Production Configuration (Optional)
# REDIS_URI=redis://localhost:6379
# POSTGRES_URI=postgresql://user:password@localhost:5432/dbname

# Development Configuration
# Set to true to enable debug logging
# DEBUG=false

# Note: Lang<PERSON>mith is no longer required for production deployment
# The agent works perfectly without Lang<PERSON>mith Cloud integration
# If you want to add Lang<PERSON><PERSON> tracing later, you can set:
# LANGSMITH_API_KEY=your_langsmith_api_key_here
# LANGCHAIN_PROJECT=your_project_name